import { NextRequest, NextResponse } from 'next/server';
import { IntelligentAgent, AgentResponse } from '@/lib/agent/intelligent-agent';
import { UserContext } from '@/lib/agent/rules-engine';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, userContext } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    // إنشاء مثيل من الوكيل الذكي
    const agent = new IntelligentAgent({
      enableCaching: true,
      maxRetries: 3,
      timeoutMs: 30000,
      debugMode: true
    });

    // معلومات الجداول (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
    const tableInfo = `
      جدول InvoiceItems:
        - ItemName: اسم المنتج
        - Quantity: الكمية
        - UnitPrice: سعر الوحدة
        - TotalAmount: المبلغ الإجمالي
        - TheDate: التاريخ
        - ClientName: اسم العميل
        - DocumentName: نوع المستند
        - DistributorName: اسم المورد
    `;

    // تنفيذ الاستعلام
    const response: AgentResponse = await agent.processQuery(query, userContext || {
      userId: 'user_001',
      role: 'manager',
      branchId: 'الرياض',
      permissions: ['read', 'write']
    }, tableInfo);

    return NextResponse.json({
      success: true,
      ...response
    });

  } catch (error) {
    console.error('خطأ في معالجة الاستعلام:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}
