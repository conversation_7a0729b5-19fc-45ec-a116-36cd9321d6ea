{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "description": "جدول يحتوي على تفاصيل الفواتير التي تشمل المبيعات والمشتريات، كل صف يمثل منتجًا في فاتورة محددة", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null, "description": "نوع الوثيقة (مثلاً فاتورة مبيعات أو فاتورة مشتريات) - يحدد نوع العملية", "isImportant": true, "category": "name"}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null, "description": "اسم المورد في حالة الشراء - أساسي لتحليل الموردين وتقارير المشتريات", "isImportant": true, "category": "name"}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null, "description": "تاريخ العملية (بيع أو شراء) - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "isImportant": true, "category": "date"}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6, "description": "سعر الوحدة للمنتج - يستخدم في حساب الإجماليات والتقارير المالية", "isImportant": true, "category": "amount"}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6, "description": "كمية المنتج المباعة أو المشتراة - مهم لتتبع المخزون وحساب الكميات المباعة", "isImportant": true, "category": "quantity"}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6, "description": "المبلغ الإجمالي للعملية - يستخدم في حساب الإجماليات والتقارير المالية", "isImportant": true, "category": "amount"}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}], "relationships": [], "extractedAt": "2025-07-21T23:50:39.352Z", "version": "1.0"}