import intentsData from './intents.json';

export interface Intent {
  id: string;
  name: string;
  description: string;
  patterns: string[];
  entities: string[];
  sql_template: string;
  confidence_threshold: number;
}

export interface Entity {
  type: string;
  value: string;
  start: number;
  end: number;
  confidence: number;
}

export interface ClassificationResult {
  intent: Intent;
  confidence: number;
  entities: Entity[];
  originalQuery: string;
  normalizedQuery: string;
}

/**
 * مصنف النوايا الذكي - يحلل استعلامات المستخدم ويحدد النية والكيانات
 */
export class IntentClassifier {
  private intents: Intent[];
  private entityPatterns: { [key: string]: { patterns: string[] } };

  constructor() {
    this.intents = intentsData.intents;
    this.entityPatterns = intentsData.entities;
  }

  /**
   * تصنيف استعلام المستخدم وتحديد النية
   */
  async classifyIntent(query: string): Promise<ClassificationResult | null> {
    const normalizedQuery = this.normalizeQuery(query);
    
    let bestMatch: { intent: Intent; confidence: number } | null = null;
    
    // البحث عن أفضل تطابق للنية
    for (const intent of this.intents) {
      const confidence = this.calculateIntentConfidence(normalizedQuery, intent);
      
      if (confidence >= intent.confidence_threshold) {
        if (!bestMatch || confidence > bestMatch.confidence) {
          bestMatch = { intent, confidence };
        }
      }
    }

    if (!bestMatch) {
      return null;
    }

    // استخراج الكيانات من الاستعلام
    const entities = this.extractEntities(normalizedQuery, bestMatch.intent.entities);

    return {
      intent: bestMatch.intent,
      confidence: bestMatch.confidence,
      entities,
      originalQuery: query,
      normalizedQuery
    };
  }

  /**
   * تطبيع الاستعلام (إزالة علامات الترقيم، توحيد المسافات، إلخ)
   */
  private normalizeQuery(query: string): string {
    return query
      .trim()
      .toLowerCase()
      .replace(/[؟!.,;:]/g, '') // إزالة علامات الترقيم العربية
      .replace(/[?!.,;:]/g, '') // إزالة علامات الترقيم الإنجليزية
      .replace(/\s+/g, ' ') // توحيد المسافات
      .trim();
  }

  /**
   * حساب مستوى الثقة في تطابق النية
   */
  private calculateIntentConfidence(query: string, intent: Intent): number {
    let maxConfidence = 0;
    
    for (const pattern of intent.patterns) {
      const normalizedPattern = this.normalizeQuery(pattern);
      const confidence = this.calculateSimilarity(query, normalizedPattern);
      maxConfidence = Math.max(maxConfidence, confidence);
    }
    
    return maxConfidence;
  }

  /**
   * حساب التشابه بين نصين
   */
  private calculateSimilarity(text1: string, text2: string): number {
    // تحويل النصوص إلى كلمات
    const words1 = text1.split(' ');
    const words2 = text2.split(' ');
    
    // حساب التطابق الدقيق
    if (text1 === text2) return 1.0;
    
    // حساب التطابق الجزئي
    if (text1.includes(text2) || text2.includes(text1)) {
      return 0.9;
    }
    
    // حساب تطابق الكلمات المشتركة
    const commonWords = words1.filter(word => words2.includes(word));
    const similarity = (commonWords.length * 2) / (words1.length + words2.length);
    
    // إضافة نقاط إضافية للكلمات المفتاحية المهمة
    const keyWords = ['مبيعات', 'شراء', 'عميل', 'منتج', 'فرع', 'مخزون'];
    let keyWordBonus = 0;
    
    for (const keyWord of keyWords) {
      if (text1.includes(keyWord) && text2.includes(keyWord)) {
        keyWordBonus += 0.1;
      }
    }
    
    return Math.min(similarity + keyWordBonus, 1.0);
  }

  /**
   * استخراج الكيانات من الاستعلام
   */
  private extractEntities(query: string, entityTypes: string[]): Entity[] {
    const entities: Entity[] = [];
    
    for (const entityType of entityTypes) {
      const entityPatterns = this.entityPatterns[entityType];
      if (!entityPatterns) continue;
      
      for (const pattern of entityPatterns.patterns) {
        const matches = this.findEntityMatches(query, pattern, entityType);
        entities.push(...matches);
      }
    }
    
    return entities;
  }

  /**
   * البحث عن تطابقات الكيانات في النص
   */
  private findEntityMatches(query: string, pattern: string, entityType: string): Entity[] {
    const entities: Entity[] = [];
    
    // معالجة الأنماط البسيطة
    if (!pattern.includes('{')) {
      const normalizedPattern = this.normalizeQuery(pattern);
      const index = query.indexOf(normalizedPattern);
      
      if (index !== -1) {
        entities.push({
          type: entityType,
          value: normalizedPattern,
          start: index,
          end: index + normalizedPattern.length,
          confidence: 0.9
        });
      }
    } else {
      // معالجة الأنماط المعقدة مع متغيرات
      const regexPattern = pattern.replace(/\{[^}]+\}/g, '([^\\s]+)');
      const regex = new RegExp(regexPattern, 'gi');
      let match;
      
      while ((match = regex.exec(query)) !== null) {
        entities.push({
          type: entityType,
          value: match[1] || match[0],
          start: match.index,
          end: match.index + match[0].length,
          confidence: 0.8
        });
      }
    }
    
    return entities;
  }

  /**
   * الحصول على جميع النوايا المتاحة
   */
  getAvailableIntents(): Intent[] {
    return this.intents;
  }

  /**
   * البحث عن نية بالمعرف
   */
  getIntentById(id: string): Intent | undefined {
    return this.intents.find(intent => intent.id === id);
  }

  /**
   * إضافة نية جديدة
   */
  addIntent(intent: Intent): void {
    this.intents.push(intent);
  }

  /**
   * تحديث نية موجودة
   */
  updateIntent(id: string, updatedIntent: Partial<Intent>): boolean {
    const index = this.intents.findIndex(intent => intent.id === id);
    if (index !== -1) {
      this.intents[index] = { ...this.intents[index], ...updatedIntent };
      return true;
    }
    return false;
  }

  /**
   * حذف نية
   */
  deleteIntent(id: string): boolean {
    const index = this.intents.findIndex(intent => intent.id === id);
    if (index !== -1) {
      this.intents.splice(index, 1);
      return true;
    }
    return false;
  }
}
