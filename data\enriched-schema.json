{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-22T03:49:00.688Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يحتوي على تفاصيل الفواتير الرئيسية للعمليات التجارية، مثل شراء المواد من الموردين وبيعها للعملاء. يتم استخدام هذا الجدول لتخزين معلومات مفصلة عن كل فاتورة، بما في ذلك رقم الفاتورة، تاريخها، تفاصيل العناصر، الأسعار، والكميات.", "columnDescriptions": {"ID": "مفتاح رئيسي فريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "معرف السجل المرتبط بالفاتورة.", "TheNumber": "رقم الفاتورة، يمكن أن يكون فارغًا.", "SupplierName": "اسم المورد، يمكن أن يكون فارغًا.", "InvoiceID": "معرف الفاتورة.", "DetailsID": "معرف التفاصيل المرتبط بالفاتورة.", "TheDate": "تاريخ الفاتورة، يمكن أن يكون فارغًا.", "CurrencyID": "معرف العملة المستخدمة في الفاتورة، يمكن أن يكون فارغًا.", "TheMethod": "طريقة الدفع، يمكن أن تكون فارغة.", "EnterTime": "وقت إدخال الفاتورة، يمكن أن يكون فارغًا.", "ItemID": "معرف العنصر، يمكن أن يكون فارغًا.", "UnitID": "معرف الوحدة، يمكن أن يكون فارغًا.", "UnitPrice": "سعر الوحدة، يمكن أن يكون فارغًا.", "Quantity": "الكمية، يمكن أن تكون فارغة.", "Bonus": "البونص، يمكن أن يكون فارغًا.", "TotalAmount": "المبلغ الإجمالي، يمكن أن يكون فارغًا.", "MainUnitQuantity": "كمية الوحدة الرئيسية، يمكن أن تكون فارغة.", "MainUnitPrice": "سعر الوحدة الرئيسية، يمكن أن يكون فارغًا.", "MainUnitID": "معرف الوحدة الرئيسية، يمكن أن يكون فارغًا.", "StoreID": "معرف المخزن، يمكن أن يكون فارغًا.", "BranchID": "معرف الفرع، يمكن أن يكون فارغًا.", "ExchangeFactor": "معامل التحويل بين الوحدات.", "ClientID": "معرف العميل، يمكن أن يكون فارغًا.", "MCAmount": "المبلغ الإجمالي بالعملة، يمكن أن يكون فارغًا.", "ExpiryDate": "تاريخ انتهاء الصلاحية، يمكن أن يكون فارغًا.", "MainUnitBonus": "بونص الوحدة الرئيسية، يمكن أن يكون فارغًا.", "ExchangePrice": "سعر الصرف، يمكن أن يكون فارغًا.", "DistributorID": "معرف الموزع، يمكن أن يكون فارغًا.", "DistributorName": "اسم الموزع، يمكن أن يكون فارغًا.", "CostCenterID": "معرف مركز التكلفة، يمكن أن يكون فارغًا.", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغًا.", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي بالعملة في المخزون، يمكن أن يكون فارغًا.", "NewSubItemEntryID": "معرف العنصر الفرعي الجديد."}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في فهم العمليات التجارية، مثل تحليل تكاليف الشراء، متابعة المبيعات، ومراقبة المخزون. يمكن استخدام البيانات لإجراء تحليلات إحصائية وترندات للبيع والشراء.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice GROUP BY SupplierName ORDER BY TotalSpent DESC;", "explanation": "هذا الاستعلام يعرض الموردين حسب إجمالي المبلغ الذي تم إنفاقه عليهم، مما يساعد في تحديد الموردين الرئيسيين."}, {"query": "SELECT TheDate, SUM(TotalAmount) AS DailySales FROM tbltemp_Inv_MainInvoice WHERE TheMethod = 'Cash' GROUP BY TheDate ORDER BY TheDate;", "explanation": "هذا الاستعلام يعرض المبيعات اليومية نقدًا، مما يساعد في متابعة الأداء اليومي للبيع نقدًا."}], "intelligentAnalysis": "الجدول يحتوي على علاقات ضمنية بين الحقول، مثل العلاقة بين ItemID و UnitID و Quantity، والتي يمكن استخدامها لتحليل تكاليف الوحدات المختلفة. كما يمكن استخدام الحقول مثل CurrencyID و ExchangePrice لتحليل تأثير الصرف على التكاليف.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتحليل بيانات الفواتير الرئيسية للعمليات التجارية، بما في ذلك الشراء والبيع.", "domain": "تجاري", "businessContext": "السياق التجاري يشمل إدارة المخزون، متابعة المبيعات، وتحليل التكاليف.", "keyFields": ["ID", "InvoiceID", "TheDate", "TotalAmount", "SupplierName", "ClientID"], "relatedTables": ["tblSuppliers", "tblClients", "tblItems", "tblCurrencies", "tblStores", "tblBranches"], "limitations": "الجدول يفتقر إلى مفاتيح خارجية، مما قد يحد من قدرة النظام على الحفاظ على سلامة البيانات. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يصعب التحليل في بعض الحالات.", "generatedAt": "2025-07-22T03:49:30.852Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على بيانات تفصيلية للبضائع والمنتجات في نظام إدارة المخزون. يتم استخدامه لتخزين معلومات متنوعة حول العناصر، مثل تفاصيل البضائع، العملاء، الموزعين، الوثائق المالية، والعمليات التجارية.", "columnDescriptions": {"ID": "مفتاح رئيسي فريد لكل سجل في الجدول", "ParentID": "مفتاح خارجي يشير إلى السجل الأبوة للعنصر الحالي، يمكن أن يكون فارغًا إذا كان العنصر ليس له سجل أب", "RowVersion": "ختم زمني يستخدم لتعقب التغييرات في السجلات", "DocumentID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الوثيقة المرتبطة بالعنصر", "RecordNumber": "رقم السجل داخل الوثيقة", "RecordID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى السجل داخل الوثيقة", "TheDate": "تاريخ إنشاء أو تحديث السجل", "ClientID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى العميل المرتبط بالعنصر", "DistributorID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الموزع المرتبط بالعنصر", "CurrencyID": "مفتا<PERSON> خارجي يشير إلى العملة المستخدمة في المعاملة", "TheMethodID": "مفتا<PERSON> خارجي يشير إلى الطريقة المستخدمة في المعاملة", "Discount": "نسبة الخصم المطبقة على العنصر", "Notes": "ملاحظات إضافية حول العنصر", "UserID": "مفتا<PERSON> خارجي يشير إلى المستخدم الذي قام بإنشاء أو تحديث السجل", "BranchID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الفرع المرتبط بالعنصر", "TheYear": "السنة التي تمت فيها المعاملة", "DocumentName": "اسم الوثيقة المرتبطة بالعنصر", "TheNumber": "رقم الوثيقة المرتبطة بالعنصر", "ClientName": "اسم العميل المرتبط بالعنصر", "DistributorName": "اسم الموزع المرتبط بالعنصر", "CurrencyName": "اسم العملة المستخدمة في المعاملة", "TheMethod": "اسم الطريقة المستخدمة في المعاملة", "UserName": "اسم المستخدم الذي قام بإنشاء أو تحديث السجل", "BranchName": "اسم الفرع المرتبط بالعنصر", "CategoryID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الفئة التي ينتمي إليها العنصر", "FatherNumber": "رقم العنصر الأب في الفئة", "CategoryName": "اسم الفئة التي ينتمي إليها العنصر", "CategoryNumber": "رقم الفئة التي ينتمي إليها العنصر", "ItemID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى العنصر نفسه", "UnitID": "مفت<PERSON><PERSON> خارجي يشير إلى الوحدة التي يتم بيع العنصر بها", "ItemNumber": "رقم العنصر داخل الفئة", "ItemName": "اسم العنصر", "ItemTypeID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى نوع العنصر", "ItemType": "نوع العنصر", "ReorderPoint": "نقطة إعادة الطلب للعنصر", "ISActive": "حالة العنصر (نشط أو غير نشط)", "ISExpiry": "هل العنصر له تاريخ انتهاء صلاحية", "ExpiryPoint": "نقطة انتهاء الصلاحية للعنصر", "UnitName": "اسم الوحدة التي يتم بيع العنصر بها", "AccountFatherNumber": "رقم الحساب الأب في النظام المحاسبي", "AccountName": "اسم الحساب في النظام المحاسبي", "AccountNumber": "رقم الحساب في النظام المحاسبي", "CostCenterID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى المركز التكلفة المرتبط بالعنصر", "CostCenterName": "اسم المركز التكلفة المرتبط بالعنصر", "CostCenterNumber": "رقم المركز التكلفة المرتبط بالعنصر", "Barcode": "الباركود الخاص بالعنصر", "UnitRank": "ترتيب الوحدة داخل العنصر", "ExchangeFactor": "معامل التحويل بين الوحدات المختلفة", "PackageQuantity": "كمية الحزمة للعنصر", "BarcodeID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الباركود الخاص بالعنصر", "SerialNumber": "رقم التسلسل للعنصر", "UnitPrice": "سعر الوحدة للعنصر", "ItemDiscount": "خصم العنصر", "McItemDiscountCurrencyMain": "خصم العنصر بالعملة الرئيسية", "McItemDiscount": "خصم العنصر بالعملة الثانوية", "Quantity": "كمية العنصر", "Bonus": "كمية المكافأة المقدمة مع العنصر", "ExpiryDate": "تاريخ انتهاء صلاحية العنصر", "Amount": "المبلغ الإجمالي للعنصر", "MCAmount": "المبلغ الإجمالي بالعملة الثانوية", "MCAmountCurrencyMain": "المبلغ الإجمالي بالعملة الرئيسية", "AccountID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الحساب المرتبط بالعنصر", "StoreID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى المخزن المرتبط بالعنصر", "StoreName": "اسم المخزن المرتبط بالعنصر", "PackageUnitID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى وحدة الحزمة للعنصر", "PackageUnitName": "اسم وحدة الحزمة للعنصر", "NextParentID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى السجل الأب التالي للعنصر", "ExchangePrice": "سعر التحويل بين العملات", "ExchangePriceCurrencyInvetory": "سعر التحويل بين العملات في المخزون"}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في فهم تفاصيل البضائع والمنتجات، ويساعد في إدارة المخزون بشكل فعال، وتحليل الأداء المالي، وتتبع العمليات التجارية. يمكن استخدام البيانات لإجراء تحليلات إحصائية وتنبؤية لتحسين إدارة المخزون وزيادة الكفاءة.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يعرض العناصر الأكثر مبيعًا حسب الكمية الإجمالية المباعة، مما يساعد في تحديد المنتجات الأكثر شعبية."}, {"query": "SELECT ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY ClientName ORDER BY TotalAmount DESC;", "explanation": "هذا الاستعلام يعرض العملاء الذين قاموا بشراء أكبر مبالغ خلال عام 2023، مما يساعد في تحديد العملاء الأكثر قيمة."}], "intelligentAnalysis": "الجدول يحتوي على بيانات متنوعة ومفصلة تسمح بإجراء تحليلات ذكية ومتعددة الأبعاد. يمكن استخدام البيانات لفهم الأنماط في المبيعات، تحليل الأداء المالي، وتتبع العمليات التجارية. كما يمكن استخدام البيانات لتحسين إدارة المخزون وزيادة الكفاءة من خلال تحليل نقاط إعادة الطلب وتاريخ انتهاء الصلاحية.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتنظيم بيانات البضائع والمنتجات في نظام إدارة المخزون، وتقديم معلومات مفصلة عن العناصر، العملاء، الموزعين، الوثائق المالية، والعمليات التجارية.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول هو إدارة المخزون والعمليات التجارية في الشركات والمؤسسات التجارية. يتم استخدام الجدول لتخزين وتحليل بيانات البضائع والمنتجات، مما يساعد في اتخاذ قرارات مستنيرة وتحسين الكفاءة.", "keyFields": ["ID", "DocumentID", "ItemID", "ClientID", "DistributorID", "CurrencyID", "TheMethodID", "UserID", "BranchID", "CategoryID", "AccountID", "StoreID", "PackageUnitID", "BarcodeID"], "relatedTables": ["tbltemp_Documents", "tbltemp_Clients", "tbltemp_Distributors", "tbltemp_Currencies", "tbltemp_Methods", "tbltemp_Users", "tbltemp_Branches", "tbltemp_Categories", "tbltemp_Accounts", "tbltemp_Stores", "tbltemp_PackageUnits", "tbltemp_Barcodes"], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة النظام على التحقق من سلامة البيانات. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يسبب مشاكل في التحليلات الإحصائية.", "generatedAt": "2025-07-22T03:50:24.143Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}