{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-21T23:50:39.352Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يحتوي على بيانات الفواتير الرئيسية وتفاصيلها، بما في ذلك معلومات عن الموردين والعملاء والبضائع والمخازن والفروع. يستخدم هذا الجدول في إدارة الفواتير والمخزون وتحليل الأداء المالي.", "columnDescriptions": {"ID": "معرّف فريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "معرّف السجل المرتبط بالفاتورة.", "TheNumber": "رقم الفاتورة، يمكن أن يكون فارغًا.", "SupplierName": "اسم المورد، يمكن أن يكون فارغًا.", "InvoiceID": "معرّف الفاتورة.", "DetailsID": "معرّف التفاصيل المرتبط بالفاتورة.", "TheDate": "تاريخ الفاتورة، يمكن أن يكون فارغًا.", "CurrencyID": "معرّف العملة المستخدمة في الفاتورة، يمكن أن يكون فارغًا.", "TheMethod": "طريقة الدفع، يمكن أن تكون فارغة.", "EnterTime": "وقت إدخال الفاتورة، يمكن أن يكون فارغًا.", "ItemID": "معرّف البند أو السلعة، يمكن أن يكون فارغًا.", "UnitID": "معرّف الوحدة، يمكن أن يكون فارغًا.", "UnitPrice": "سعر الوحدة، يمكن أن يكون فارغًا.", "Quantity": "الكمية، يمكن أن تكون فارغة.", "Bonus": "البونص أو الخصم، يمكن أن يكون فارغًا.", "TotalAmount": "المبلغ الإجمالي، يمكن أن يكون فارغًا.", "MainUnitQuantity": "كمية الوحدة الرئيسية، يمكن أن تكون فارغة.", "MainUnitPrice": "سعر الوحدة الرئيسية، يمكن أن يكون فارغًا.", "MainUnitID": "معرّف الوحدة الرئيسية، يمكن أن يكون فارغًا.", "StoreID": "معرّف المخزن، يمكن أن يكون فارغًا.", "BranchID": "معرّف الفرع، يمكن أن يكون فارغًا.", "ExchangeFactor": "معامل الصرف.", "ClientID": "معرّف العميل، يمكن أن يكون فارغًا.", "MCAmount": "المبلغ الإجمالي بالعملة المحلية، يمكن أن يكون فارغًا.", "ExpiryDate": "تاريخ انتهاء الصلاحية، يمكن أن يكون فارغًا.", "MainUnitBonus": "بونص الوحدة الرئيسية، يمكن أن يكون فارغًا.", "ExchangePrice": "سعر الصرف، يمكن أن يكون فارغًا.", "DistributorID": "معرّف الموزع، يمكن أن يكون فارغًا.", "DistributorName": "اسم الموزع، يمكن أن يكون فارغًا.", "CostCenterID": "معرّف مركز التكلفة، يمكن أن يكون فارغًا.", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغًا.", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي بالعملة المخزونية، يمكن أن يكون فارغًا.", "NewSubItemEntryID": "معرّف البند الفرعي الجديد."}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في إدارة الفواتير والمخزون، حيث يمكن استخدامه لتحليل الأداء المالي، تتبع حركة البضائع، وإدارة العلاقات مع الموردين والعملاء. يمكن استخدام البيانات الموجودة في الجدول لإجراء تحليلات إحصائية وترندات زمنية.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice GROUP BY SupplierName ORDER BY TotalSpent DESC;", "explanation": "هذا الاستعلام يجمع المبالغ الإجمالية المصروفة لكل مورد وترتيبها تنازليًا. يمكن استخدامه لتحديد الموردين الأكثر تكلفة."}, {"query": "SELECT TheDate, COUNT(*) AS InvoiceCount FROM tbltemp_Inv_MainInvoice WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY TheDate ORDER BY TheDate;", "explanation": "هذا الاستعلام يحسب عدد الفواتير الصادرة في كل يوم خلال عام 2023. يمكن استخدامه لتحليل ترندات الفواتير الزمنية."}, {"query": "SELECT ItemID, SUM(Quantity) AS TotalQuantity FROM tbltemp_Inv_MainInvoice GROUP BY ItemID ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يجمع الكمية الإجمالية لكل بند وترتيبها تنازليًا. يمكن استخدامه لتحديد الأصناف الأكثر طلبًا."}], "intelligentAnalysis": "الجدول يحتوي على بيانات مفصلة عن الفواتير والبضائع والمخازن، مما يسمح بإجراء تحليلات ذكية مثل تحليل الأداء المالي، تتبع حركة البضائع، وإدارة العلاقات مع الموردين والعملاء. يمكن استخدام البيانات لتحسين عمليات الشراء والمخزون وتقليل التكاليف.", "purpose": "الغرض الأساسي من الجدول هو إدارة الفواتير والمخزون وتحليل الأداء المالي للشركة.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول يشمل إدارة الفواتير، المخزون، العلاقات مع الموردين والعملاء، والتحليل المالي.", "keyFields": ["ID", "InvoiceID", "ItemID", "SupplierName", "ClientID", "TheDate", "TotalAmount"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة التكامل مع جداول أخرى. بعض الحقول يمكن أن تكون فارغة، مما قد يؤثر على دقة التحليلات.", "generatedAt": "2025-07-21T23:51:10.137Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على معلومات تفصيلية عن العناصر والبضائع في نظام إدارة المخزون والمحاسبة. يتم استخدام هذا الجدول لتخزين تفاصيل العناصر مثل معلومات العميل، الموزع، العملة، طريقة الدفع، الخصم، الكمية، السعر، وتاريخ الانتهاء.", "columnDescriptions": {"ID": "مفتاح رئيسي فريد لكل سجل في الجدول", "ParentID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى السجل الأبوة في حالة وجود عناصر فرعية", "RowVersion": "ختم زمني يستخدم لتعقب التغييرات في السجل", "DocumentID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الوثيقة المرتبطة بالعنصر", "RecordNumber": "رقم السجل داخل الوثيقة", "RecordID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى السجل المرتبط بالعنصر", "TheDate": "تاريخ إدخال أو تحديث السجل", "ClientID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى العميل المرتبط بالعنصر", "DistributorID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الموزع المرتبط بالعنصر", "CurrencyID": "مفتا<PERSON> خارجي يشير إلى العملة المستخدمة في المعاملة", "TheMethodID": "مفت<PERSON><PERSON> خارجي يشير إلى طريقة الدفع المستخدمة", "Discount": "نسبة الخصم المطبقة على العنصر", "Notes": "ملاحظات إضافية حول العنصر", "UserID": "مفتا<PERSON> خارجي يشير إلى المستخدم الذي أدخل أو تحديث السجل", "BranchID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الفرع المرتبط بالعنصر", "TheYear": "السنة التي تم فيها إدخال السجل", "DocumentName": "اسم الوثيقة المرتبطة بالعنصر", "TheNumber": "رقم الوثيقة المرتبطة بالعنصر", "ClientName": "اسم العميل المرتبط بالعنصر", "DistributorName": "اسم الموزع المرتبط بالعنصر", "CurrencyName": "اسم العملة المستخدمة في المعاملة", "TheMethod": "اسم طريقة الدفع المستخدمة", "UserName": "اسم المستخدم الذي أدخل أو تحديث السجل", "BranchName": "اسم الفرع المرتبط بالعنصر", "CategoryID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الفئة المرتبطة بالعنصر", "FatherNumber": "رقم العنصر الأب في حالة وجود عناصر فرعية", "CategoryName": "اسم الفئة المرتبطة بالعنصر", "CategoryNumber": "رقم الفئة المرتبطة بالعنصر", "ItemID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى العنصر المرتبط بالسجل", "UnitID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الوحدة المرتبطة بالعنصر", "ItemNumber": "رقم العنصر داخل الوثيقة", "ItemName": "اسم العنصر", "ItemTypeID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى نوع العنصر", "ItemType": "نوع العنصر", "ReorderPoint": "نقطة إعادة الطلب للعنصر", "ISActive": "حالة العنصر (نشط أو غير نشط)", "ISExpiry": "حالة انتهاء صلاحية العنصر", "ExpiryPoint": "نقطة انتهاء صلاحية العنصر", "UnitName": "اسم الوحدة المرتبطة بالعنصر", "AccountFatherNumber": "رقم الحساب الأب في حالة وجود حسابات فرعية", "AccountName": "اسم الحساب المرتبط بالعنصر", "AccountNumber": "رقم الحساب المرتبط بالعنصر", "CostCenterID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى مركز التكلفة المرتبط بالعنصر", "CostCenterName": "اسم مركز التكلفة المرتبط بالعنصر", "CostCenterNumber": "رقم مركز التكلفة المرتبط بالعنصر", "Barcode": "ر<PERSON>ز الباركود للعنصر", "UnitRank": "ترتيب الوحدة المرتبطة بالعنصر", "ExchangeFactor": "معامل التبادل للعنصر", "PackageQuantity": "كمية العبوة للعنصر", "BarcodeID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى رمز الباركود المرتبط بالعنصر", "SerialNumber": "رقم التسلسل للعنصر", "UnitPrice": "سعر الوحدة للعنصر", "ItemDiscount": "خصم العنصر", "McItemDiscountCurrencyMain": "خصم العنصر بالعملة الرئيسية", "McItemDiscount": "خصم العنصر بالعملة الثانوية", "Quantity": "كمية العنصر", "Bonus": "كمية المكافأة للعنصر", "ExpiryDate": "تاريخ انتهاء صلاحية العنصر", "Amount": "المبلغ الإجمالي للعنصر", "MCAmount": "المبلغ الإجمالي بالعملة الثانوية", "MCAmountCurrencyMain": "المبلغ الإجمالي بالعملة الرئيسية", "AccountID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى الحساب المرتبط بالعنصر", "StoreID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى المخزن المرتبط بالعنصر", "StoreName": "اسم المخزن المرتبط بالعنصر", "PackageUnitID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى وحدة العبوة المرتبط بالعنصر", "PackageUnitName": "اسم وحدة العبوة المرتبط بالعنصر", "NextParentID": "مف<PERSON><PERSON><PERSON> خارجي يشير إلى السجل الأب التالي في حالة وجود عناصر فرعية", "ExchangePrice": "سعر التبادل للعنصر", "ExchangePriceCurrencyInvetory": "سعر التبادل للعنصر بالعملة الرئيسية"}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في مجال إدارة المخزون والمحاسبة، حيث يمكن استخدامه لتحليل الاتجاهات في مبيعات العناصر، تقييم أداء الموزعين والعملاء، ومراقبة مستويات المخزون ونقاط إعادة الطلب. كما يمكن استخدامه لتحليل الخصومات والتكاليف المرتبطة بالعناصر.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يوفر تقريرًا يوضح العناصر الأكثر مبيعًا بناءً على الكمية والمبلغ الإجمالي. يمكن استخدام هذا التقرير لتحليل الاتجاهات في المبيعات وتحديد العناصر الأكثر ربحية."}, {"query": "SELECT ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE TheDate BETWEEN '2023-01-01' AND '2023-12-31' GROUP BY ClientName ORDER BY TotalAmount DESC;", "explanation": "هذا الاستعلام يوفر تقريرًا يوضح العملاء الأكثر إنفاقًا خلال سنة معينة. يمكن استخدام هذا التقرير لتحديد العملاء الأكثر قيمة وتقديم عروض خاصة لهم."}, {"query": "SELECT ItemName, AVG(UnitPrice) AS AveragePrice FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY AveragePrice DESC;", "explanation": "هذا الاستعلام يوفر تقريرًا يوضح متوسط سعر الوحدة للعناصر. يمكن استخدام هذا التقرير لتحليل أسعار العناصر وتحديد العناصر التي تحتاج إلى إعادة تسعير."}], "intelligentAnalysis": "الجدول يحتوي على بيانات مفصلة ومتنوعة يمكن استخدامها لتحليل الاتجاهات في المبيعات، تقييم أداء الموزعين والعملاء، ومراقبة مستويات المخزون. يمكن استخدام البيانات لتحسين إدارة المخزون، تحسين تسعير العناصر، وتقديم عروض خاصة للعملاء الأكثر قيمة. كما يمكن استخدام البيانات لتحليل الخصومات والتكاليف المرتبطة بالعناصر.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتحليل معلومات تفصيلية عن العناصر والبضائع في نظام إدارة المخزون والمحاسبة.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول هو إدارة المخزون والمحاسبة في الشركات التجارية. يتم استخدام الجدول لتخزين تفاصيل العناصر والبضائع، ومعلومات العملاء والموزعين، والمعاملات المالية المرتبطة بها.", "keyFields": ["ID", "DocumentID", "RecordNumber", "ItemID", "ItemNumber", "Quantity", "Amount", "TheDate"], "relatedTables": ["tblClients", "tblDistributors", "tblCurrencies", "tblPaymentMethods", "tblUsers", "tblBranches", "tblCategories", "tblItems", "tblUnits", "tblCostCenters", "tblAccounts", "tblStores"], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من القدرة على التحقق من سلامة البيانات. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يسبب مشاكل في التحليل إذا لم يتم التعامل معها بشكل صحيح.", "generatedAt": "2025-07-21T23:52:05.428Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}