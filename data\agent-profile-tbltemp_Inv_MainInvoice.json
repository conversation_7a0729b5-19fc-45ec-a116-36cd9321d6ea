{"table": "tbltemp_Inv_MainInvoice", "description": "جدول يحتوي على تفاصيل الفواتير التي تشمل المبيعات والمشتريات، كل صف يمثل منتجًا في فاتورة محددة يخدم مجال sales_and_inventory ويهدف إلى تتبع المبيعات والمخزون. يحتوي على 17 عمود مهم من أصل 17 عمود.", "fields": {"ID": "معرف فريد للسجل", "DocumentName": "نوع الوثيقة (مثلاً فاتورة مبيعات أو فاتورة مشتريات) - يحدد نوع العملية - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)", "RecordID": "معرف فريد للسجل", "TheNumber": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "SupplierName": "اسم المورد في حالة الشراء - أساسي لتحليل الموردين وتقارير المشتريات", "InvoiceID": "معرف الفاتورة", "DetailsID": "معرف فريد للسجل", "TheDate": "تاريخ العملية (بيع أو شراء) - يستخدم في التقارير الزمنية وتحليل الاتجاهات - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "CurrencyID": "معرف فريد للسجل", "TheMethod": "اسم أو وصف", "EnterTime": "التاريخ - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "ItemID": "معر<PERSON> المنتج", "UnitID": "معرف فريد للسجل", "UnitPrice": "سعر الوحدة للمنتج - يستخدم في حساب الإجماليات والتقارير المالية", "Quantity": "كمية المنتج المباعة أو المشتراة - مهم لتتبع المخزون وحساب الكميات المباعة - مهم لتتبع المخزون وحساب الكميات المباعة", "Bonus": "الكمية - مهم لتتبع المخزون وحساب الكميات المباعة", "TotalAmount": "المبلغ الإجمالي للعملية - يستخدم في حساب الإجماليات والتقارير المالية - يستخدم في حساب الإجماليات والتقارير المالية"}, "use_cases": [{"name": "إجمالي المبيعات لفترة معينة", "description": "حساب مجموع المبيعات خلال فترة زمنية محددة", "example_sql": "SELECT SUM(UnitPrice) as total_sales FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' AND TheDate BETWEEN @start_date AND @end_date", "intent_category": "sales_total", "parameters": ["start_date", "end_date"]}, {"name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً حسب الكمية", "example_sql": "SELECT TOP 10 ItemID, SUM(TheNumber) as total_quantity FROM tbltemp_Inv_MainInvoice WHERE DocumentName LIKE '%مبيعات%' GROUP BY ItemID ORDER BY total_quantity DESC", "intent_category": "top_products", "parameters": ["limit"]}, {"name": "حالة المخزون", "description": "عرض الكميات المتاحة للمنتجات", "example_sql": "SELECT ItemID, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN -TheNumber WHEN DocumentName LIKE '%مشتريات%' THEN TheNumber ELSE 0 END) as current_stock FROM tbltemp_Inv_MainInvoice GROUP BY ItemID", "intent_category": "inventory_status", "parameters": []}], "business_context": {"domain": "sales_and_inventory", "primary_purpose": "تتبع المبيعات والمخزون", "key_metrics": ["المبالغ المالية", "الإجماليات", "الكميات", "المخزون"], "common_filters": ["فلتر التاريخ", "فترة زمنية", "نوع المستند"]}, "sql_patterns": {"aggregations": ["SUM(Amount) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي المبالغ", "AVG(Amount) - لحسا<PERSON> متوسط المبالغ", "SUM(Quantity) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي الكميات", "COUNT(*) - لعد السجلات"], "joins": [], "filters": ["WHERE TheDate BETWEEN @start_date AND @end_date - فلتر التاريخ", "WHERE YEAR(TheDate) = @year - فلتر السنة", "WHERE MONTH(TheDate) = @month - فلتر الشهر", "WHERE DocumentName LIKE '%مبيعات%' - فلتر المبيعات", "WHERE DocumentName LIKE '%مشتريات%' - فلتر المشتريات"]}}