{"intents": [{"id": "sales_total", "name": "إجمالي المبيعات", "description": "الحصول على إجمالي المبيعات لفترة معينة", "patterns": ["إجمالي المبيعات", "مجموع المبيعات", "كم المبيعات", "قيمة المبيعات", "أر<PERSON><PERSON><PERSON> المبيعات", "مبيعات اليوم", "مبيعات الشهر", "مبيعات السنة"], "entities": ["date_range", "branch", "product"], "sql_template": "sales_total", "confidence_threshold": 0.8}, {"id": "top_products", "name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً", "patterns": ["أكثر المنتجات مبيعاً", "أفضل المنتجات", "المنتجات الأكثر طلباً", "أكثر الأصناف مبيعاً", "المنتجات الرائجة", "أ<PERSON>هر المنتجات"], "entities": ["date_range", "branch", "limit"], "sql_template": "top_products", "confidence_threshold": 0.8}, {"id": "customer_analysis", "name": "تحليل العملاء", "description": "تحليل بيانات العملاء والمشتريات", "patterns": ["أكثر العملاء شراءً", "أفضل العملاء", "العملاء الأكثر قيمة", "تحليل العملاء", "عملاء VIP", "أهم العملاء"], "entities": ["date_range", "branch", "limit"], "sql_template": "customer_analysis", "confidence_threshold": 0.8}, {"id": "inventory_status", "name": "حالة المخزون", "description": "عرض حالة المخزون والكميات المتاحة", "patterns": ["حالة المخزون", "الكميات المتاحة", "المخزون الحالي", "الأصناف المتوفرة", "نفاد المخزون", "الأصناف الناقصة"], "entities": ["product", "store", "branch"], "sql_template": "inventory_status", "confidence_threshold": 0.8}, {"id": "purchase_analysis", "name": "تحليل المشتريات", "description": "تحليل بيانات المشتريات والموردين", "patterns": ["إجمالي المشتريات", "مشتريات من مورد", "أكثر الموردين", "تحليل المشتريات", "فواتير الشراء", "مصروفات المشتريات"], "entities": ["date_range", "supplier", "branch"], "sql_template": "purchase_analysis", "confidence_threshold": 0.8}, {"id": "financial_reports", "name": "التقارير المالية", "description": "التقارير المالية والربحية", "patterns": ["الأرباح", "الخسائر", "التقارير المالية", "الربحية", "المصروفات", "الإيرادات", "التد<PERSON>ق النقدي"], "entities": ["date_range", "branch", "cost_center"], "sql_template": "financial_reports", "confidence_threshold": 0.8}, {"id": "branch_performance", "name": "أداء الفروع", "description": "مقارنة أداء الفروع المختلفة", "patterns": ["أداء الفروع", "مقارنة الفروع", "أفضل فرع", "أكثر الفروع مبيعاً", "تحليل الفروع", "إحصائيات الفروع"], "entities": ["date_range", "branch", "metric"], "sql_template": "branch_performance", "confidence_threshold": 0.8}, {"id": "time_analysis", "name": "التحليل الزمني", "description": "تحليل البيانات عبر فترات زمنية مختلفة", "patterns": ["مبيعات شهرية", "مبيعات سنوية", "مبيعات يومية", "اتجاه المبيعات", "نمو المبيعات", "مقارنة الفترات"], "entities": ["date_range", "period_type", "comparison"], "sql_template": "time_analysis", "confidence_threshold": 0.8}, {"id": "product_movement", "name": "حركة الأصناف", "description": "تتبع حركة الأصناف والمخزون", "patterns": ["حركة صنف", "تاريخ المنتج", "حركة المخزون", "دخول وخروج", "تتبع الصنف", "سجل المنتج"], "entities": ["product", "date_range", "movement_type"], "sql_template": "product_movement", "confidence_threshold": 0.8}, {"id": "returns_analysis", "name": "تحليل المرتجعات", "description": "تحليل المرتجعات والإرجاعات", "patterns": ["المرتجعات", "الإرجاعات", "المنتجات المرتجعة", "أسباب الإرجاع", "تحليل المرتجعات", "نسبة المرتجعات"], "entities": ["date_range", "product", "return_reason"], "sql_template": "returns_analysis", "confidence_threshold": 0.8}], "entities": {"date_range": {"patterns": ["اليوم", "<PERSON><PERSON><PERSON>", "هذا الأسبوع", "الأسبوع الماضي", "هذا الشهر", "الشهر الماضي", "هذه السنة", "السنة الماضية", "آخر 7 أيام", "آخر 30 يوم", "آخر 3 شهور", "من {date} <PERSON><PERSON><PERSON> {date}"]}, "branch": {"patterns": ["فرع {name}", "في فرع {name}", "من فرع {name}", "الفرع الرئيسي", "جميع الفروع"]}, "product": {"patterns": ["منتج {name}", "صنف {name}", "{name}", "المنتج {name}", "الصنف {name}"]}, "customer": {"patterns": ["عميل {name}", "العميل {name}", "الزبون {name}", "{name}"]}, "supplier": {"patterns": ["مور<PERSON> {name}", "المورد {name}", "من مورد {name}", "{name}"]}}}