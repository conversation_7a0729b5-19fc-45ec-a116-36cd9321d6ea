{"tables": [{"name": "tbltemp_ItemsMain", "description": "جدول يحتوي على تفاصيل عناصر الفواتير مثل المبيعات والمشتريات.", "columns": [{"name": "ID", "type": "bigint", "description": "المعرف الأساسي لكل سجل", "isImportant": true, "category": "id"}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>(200)", "description": "اسم المنتج أو الصنف", "isImportant": true, "category": "name"}, {"name": "Quantity", "type": "numeric(18,6)", "description": "كمية المنتج المباعة أو المشتراة", "isImportant": true, "category": "quantity"}, {"name": "TheDate", "type": "datetime", "description": "تاريخ العملية (بيع أو شراء)", "isImportant": true, "category": "date"}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "نوع الوثيقة (مثلاً فاتورة مبيعات أو فاتورة مشتريات)", "isImportant": true, "category": "reference"}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم الفرع الذي تمت فيه العملية", "isImportant": true, "category": "name"}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم العميل في حالة البيع", "isImportant": true, "category": "name"}, {"name": "UnitPrice", "type": "numeric(18,6)", "description": "سعر الوحدة للمنتج", "isImportant": true, "category": "amount"}, {"name": "Amount", "type": "numeric(18,6)", "description": "المبلغ الإجمالي للعملية", "isImportant": true, "category": "amount"}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المورد في حالة الشراء", "isImportant": true, "category": "name"}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>(150)", "description": "اسم المخزن", "isImportant": true, "category": "name"}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>(100)", "description": "اسم المستخدم الذي أدخل البيانات", "isImportant": false, "category": "other"}, {"name": "CreatedAt", "type": "datetime", "description": "تاريخ إنشاء السجل", "isImportant": false, "category": "date"}, {"name": "UpdatedAt", "type": "datetime", "description": "تاريخ آخر تحديث للسجل", "isImportant": false, "category": "date"}], "primaryKey": "ID"}], "version": "1.0.0", "generatedAt": "2024-01-15T10:30:00.000Z", "metadata": {"totalTables": 1, "totalColumns": 14, "importantColumns": 11}}