{"table": "tbltemp_ItemsMain", "description": "جدول يحتوي على تفاصيل الفواتير التي تشمل المبيعات والمشتريات، كل صف يمثل منتجًا في فاتورة محددة. يخدم مجال sales_and_inventory ويهدف إلى تتبع المبيعات والمخزون. يحتوي على 11 عمود مهم من أصل 14 عمود.", "fields": {"ID": "المعرف الأساسي لكل سجل - أساسي لربط البيانات", "ItemName": "اسم المنتج أو الصنف - أساسي لتحليل العملاء وتقارير المبيعات", "Quantity": "كمية المنتج المباعة أو المشتراة - مهم لتتبع المخزون وحساب الكميات المباعة", "TheDate": "تاريخ العملية (بيع أو شراء) - يستخدم في التقارير الزمنية وتحليل الاتجاهات", "DocumentName": "نوع الوثيقة (مثلاً فاتورة مبيعات أو فاتورة مشتريات) - يحدد نوع العملية (مبيعات، مشتريات، مرتجعات)", "BranchName": "اسم الفرع الذي تمت فيه العملية - يستخدم في مقارنة أداء الفروع", "ClientName": "اسم العميل في حالة البيع - أساسي لتحليل العملاء وتقارير المبيعات", "UnitPrice": "سعر الوحدة للمنتج - يستخدم في حساب الإجماليات والتقارير المالية", "Amount": "المبلغ الإجمالي للعملية - يستخدم في حساب الإجماليات والتقارير المالية", "DistributorName": "اسم المورد في حالة الشراء - أساسي لتحليل العملاء وتقارير المبيعات", "StoreName": "اسم المخزن - يستخدم في مقارنة أداء الفروع"}, "use_cases": [{"name": "إجمالي المبيعات لفترة معينة", "description": "حساب مجموع المبيعات خلال فترة زمنية محددة", "example_sql": "SELECT SUM(Amount) as total_sales FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' AND TheDate BETWEEN @start_date AND @end_date", "intent_category": "sales_total", "parameters": ["start_date", "end_date"]}, {"name": "أكثر المنتجات مبيعاً", "description": "عرض المنتجات الأكثر مبيعاً حسب الكمية", "example_sql": "SELECT TOP 10 ItemName, SUM(Quantity) as total_quantity FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' GROUP BY ItemName ORDER BY total_quantity DESC", "intent_category": "top_products", "parameters": ["limit"]}, {"name": "أفضل العملاء", "description": "تحديد العملاء الذين حققوا أعلى مبيعات", "example_sql": "SELECT TOP 10 ClientName, SUM(Amount) as total_amount FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' GROUP BY ClientName ORDER BY total_amount DESC", "intent_category": "customer_analysis", "parameters": ["limit"]}, {"name": "أداء الفروع", "description": "مقارنة أداء الفروع المختلفة من حيث المبيعات", "example_sql": "SELECT BranchName, SUM(Amount) as branch_sales FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' GROUP BY BranchName ORDER BY branch_sales DESC", "intent_category": "branch_performance", "parameters": []}, {"name": "حالة المخزون", "description": "عرض الكميات المتاحة للمنتجات", "example_sql": "SELECT ItemName, SUM(CASE WHEN DocumentName LIKE '%مبيعات%' THEN -Quantity WHEN DocumentName LIKE '%مشتريات%' THEN Quantity ELSE 0 END) as current_stock FROM tbltemp_ItemsMain GROUP BY ItemName", "intent_category": "inventory_status", "parameters": []}, {"name": "أكثر عميل شراءً في فرع معين", "description": "استعلام يحدد العميل الذي قام بأكبر حجم شراء في فرع معين خلال فترة زمنية محددة.", "example_sql": "SELECT TOP 1 ClientName, SUM(Amount) AS TotalAmount FROM tbltemp_ItemsMain WHERE DocumentName LIKE '%مبيعات%' AND BranchName = @branch_name AND TheDate BETWEEN @start_date AND @end_date GROUP BY ClientName ORDER BY TotalAmount DESC", "intent_category": "customer_analysis", "parameters": ["branch_name", "start_date", "end_date"]}], "business_context": {"domain": "sales_and_inventory", "primary_purpose": "تتبع المبيعات والمخزون", "key_metrics": ["المبالغ المالية", "الإجماليات", "الكميات", "المخزون", "<PERSON><PERSON><PERSON> العملاء", "تحليل العملاء"], "common_filters": ["فلتر التاريخ", "فترة زمنية", "فلتر الفرع", "نوع المستند"]}, "sql_patterns": {"aggregations": ["SUM(Amount) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي المبالغ", "AVG(Amount) - لحسا<PERSON> متوسط المبالغ", "SUM(Quantity) - ل<PERSON><PERSON><PERSON><PERSON> إجمالي الكميات", "COUNT(*) - لعد السجلات"], "joins": [], "filters": ["WHERE TheDate BETWEEN @start_date AND @end_date - فلتر التاريخ", "WHERE YEAR(TheDate) = @year - فلتر السنة", "WHERE MONTH(TheDate) = @month - فلتر الشهر", "WHERE DocumentName LIKE '%مبيعات%' - فلتر المبيعات", "WHERE DocumentName LIKE '%مشتريات%' - فلتر المشتريات", "WHERE BranchName = @branch_name - فلتر الفرع"]}}